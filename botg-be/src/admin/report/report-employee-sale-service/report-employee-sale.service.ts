import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest, GetManyDefaultResponse } from 'src/core/crud/crud';
import { Between, Brackets, ILike, In, IsNull, Repository } from 'typeorm';
import * as moment from 'moment';
import {
  InvoiceStatus,
  ProductType,
  PaymentMethod,
  CreditType,
} from 'src/core/enums/entity';
import { ReportQueryDTO } from '../dto/report.dto';
import { Invoice } from 'src/admin/invoice/invoice.entity';
import { ReportEmployeeSaleServiceQueryDTO } from './dto/report-employee-sale-service.dto';
import { ReportPrepaidServiceSaleQueryDTO } from './dto/report-prepaid-service-sale.dto';
import { Order } from 'src/admin/order/order.entity';
import { User } from 'src/admin/user/user.entity';
import { OrderDetail } from 'src/admin/order-detail/order-detail.entity';
import {
  customHandlePaging,
  getCustomPaginationLimit,
} from 'src/core/common/common.utils';
import { Employee } from 'src/admin/employee/employee.entity';
import { createPaginationReportInfo } from '../utils/paginate.utils';

@Injectable()
export class ReportEmployeeSaleService {
  constructor(
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(Order)
    private orderRepo: Repository<Order>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(Employee)
    private employeeRepo: Repository<Employee>,
  ) {}

  async getReportEmployeeList(
    req: CrudRequest,
    branchIds: string[],
    { keySearch, page, limit, showInactiveTherapists }: ReportQueryDTO,
    isExport = false,
  ) {
    const checkShowInactive = showInactiveTherapists == 'true';
    const queryBuilder = this.employeeRepo.createQueryBuilder('employee');
    queryBuilder.leftJoin('employee.gender', 'gender');
    queryBuilder.leftJoin('employee.salaryType', 'salaryType');
    queryBuilder.leftJoin('employee.status', 'status');
    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where('employee.fullName ILIKE :fullName', {
              fullName: `%${keySearch}%`,
            })
            .orWhere('employee."displayName" ILIKE :displayName', {
              displayName: `%${keySearch}%`,
            })
            .orWhere('employee."phone" ILIKE :phone', {
              phone: `%${keySearch}%`,
            })
            .orWhere('employee.nric::text ILIKE :nric', {
              nric: `%${keySearch}%`,
            }),
        ),
      );
    }

    if (!checkShowInactive) {
      queryBuilder.andWhere(`status.name = :status`, { status: 'Active' });
    }

    if (branchIds.length > 0) {
      queryBuilder.andWhere('employee.branchId IN (:...branchIds)', {
        branchIds,
      });
    }

    queryBuilder.addSelect(['gender.id', 'gender.name']);
    queryBuilder.addSelect(['salaryType.id', 'salaryType.name']);
    queryBuilder.orderBy('employee.fullName', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }
    const [employeeAll, total] = await queryBuilder.getManyAndCount();

    const formatDateToDDMMYYYY = (date) => {
      const day = String(date?.getDate()).padStart(2, '0');
      const month = String(date?.getMonth() + 1).padStart(2, '0');
      const year = date?.getFullYear();

      return `${day}/${month}/${year}`;
    };

    const fomartedEmployee = employeeAll.map((employee, index) => {
      return {
        order: index + 1,
        id: employee.id,
        fullName: employee.fullName,
        displayName: employee.displayName,
        nric: employee.nric,
        gender: employee.gender?.name,
        birthDay:
          isExport && employee.birthDay
            ? formatDateToDDMMYYYY(employee.birthDay)
            : employee.birthDay,
        phone: employee.phone,
        salaryType: employee.salaryType?.name,
        employeeCPF: employee.employeeCPF,
        employerCPF: employee.employerCPF,
        email: employee.email,
        address: employee.address,
      };
    });
    if (isExport) {
      return fomartedEmployee;
    }
    return createPaginationReportInfo(
      fomartedEmployee,
      total,
      qLimit || total,
      offset || 0,
    );
  }

  private normalizeDateRange(
    startDate: string,
    endDate: string,
    clientZoneName?: string,
  ) {
    let normalizedStartDate = startDate;
    let normalizedEndDate = endDate;
    if (startDate && clientZoneName) {
      normalizedStartDate = moment
        .tz(startDate, clientZoneName)
        .startOf('day')
        .toISOString();
    }
    if (endDate && clientZoneName) {
      normalizedEndDate = moment
        .tz(endDate, clientZoneName)
        .endOf('day')
        .toISOString();
    }
    return { normalizedStartDate, normalizedEndDate };
  }

  async getReportNewCustomerSale(
    req: CrudRequest,
    {
      startDate,
      endDate,
      categoryIds,
      treatedById,
      showConversionSummary,
      clientZoneName,
    }: ReportEmployeeSaleServiceQueryDTO,
    isExport = false,
  ) {
    showConversionSummary =
      showConversionSummary &&
      (showConversionSummary === 'true' || showConversionSummary == true)
        ? true
        : false;
    let categoryArr = [];
    if (categoryIds) {
      categoryArr = categoryIds.split(',') || [];
    }

    // Normalize startDate and endDate to UTC+0 using clientZoneName
    const { normalizedStartDate, normalizedEndDate } = this.normalizeDateRange(
      startDate,
      endDate,
      clientZoneName,
    );

    const utcStartTime = normalizedStartDate;
    const utcEndTime = normalizedEndDate;

    const whereClause: Record<string, any> = {
      status: In([InvoiceStatus.PAID]),
      created: Between(utcStartTime, utcEndTime),
      // customer: {
      //   firstVisit: Between(utcStartTime, utcEndTime),
      // },
    };

    if (categoryArr.length > 0) {
      whereClause.orders = {
        items: {
          product: {
            category: {
              id: In(categoryArr),
            },
          },
        },
      };
    }
    if (treatedById) {
      const saleEmployeeArr = treatedById.split(',');
      whereClause.referral = {
        id: In(saleEmployeeArr),
      };
    }
    const invoiceOfCustomer = await this.invoiceRepo.find({
      where: whereClause,
      order: {
        created: 'ASC',
      },
      relations: [
        'orders',
        'referral',
        'customer',
        'invoicePayments',
        'invoicePayments.paymentMethod',
      ],
    });
    //grand total for quantity, sales
    const grandTotal = {
      quantity: 0,
      sales: 0,
      price: 'GRAND TOTAL:',
      remark: '',
      item: '',
    };
    let modifiedInvoiceCustomer = [];
    if (invoiceOfCustomer.length > 0) {
      const CustomerListPromises = invoiceOfCustomer.map(async (invoice) => {
        // Detect if invoice is paid by credit
        const isPaidByCredit = Array.isArray(invoice.invoicePayments)
          ? invoice.invoicePayments.some(
              (payment) =>
                payment?.paymentMethod?.code?.toLowerCase() ===
                  PaymentMethod.CREDIT ||
                payment?.paymentMethod?.code === CreditType.NEW ||
                payment?.paymentMethod?.code === CreditType.OLD,
            )
          : false;
        const itemsAllCategory = [];
        const itemsByCategory = [];
        let countAllCategory = 0;
        let countByCategory = 0;
        const diff = invoice.paid - invoice.subTotal;
        const percent = Math.abs(diff / invoice.subTotal) * 100;
        invoice.code = `IN${invoice.code}`;
        if (invoice?.orders?.length > 0) {
          for (const order of invoice?.orders) {
            const orderPayload: any = order.payload;
            if (orderPayload && orderPayload.items) {
              const items: any[] = orderPayload.items;
              for (const item of items) {
                const money =
                  (item?.quantity ?? 1) * (item?.product?.price ?? 0);
                // If paid by credit, sales = 0.00
                const salesValue = isPaidByCredit
                  ? '0.00'
                  : (diff >= 0
                      ? money + (money * percent) / 100
                      : money - (money * percent) / 100
                    ).toFixed(2);
                if (
                  categoryArr.length > 0 &&
                  categoryArr.includes(item?.product?.category?.id)
                ) {
                  const objectService = {
                    remark: item?.note,
                    item: item?.product?.name ?? '',
                    price: (item?.product?.price ?? 0).toFixed(2),
                    quantity: item?.quantity ?? 1,
                    sales: salesValue,
                  };
                  itemsByCategory[countByCategory] = objectService;
                  countByCategory++;
                } else {
                  const objectService = {
                    remark: item?.note,
                    item: item?.product?.name ?? '',
                    price: (item?.product?.price ?? 0).toFixed(2),
                    quantity: item?.quantity ?? 1,
                    sales: salesValue,
                  };
                  itemsAllCategory[countAllCategory] = objectService;
                  countAllCategory++;
                }
              }
            }
          }
        }
        let itemsData = itemsAllCategory;
        if (categoryArr.length > 0) {
          itemsData = itemsByCategory;
        }
        delete invoice.orders;
        return {
          ...invoice,
          itemsData,
        };
      });

      modifiedInvoiceCustomer = await Promise.all(CustomerListPromises);

      for (const invoice of modifiedInvoiceCustomer) {
        for (const item of invoice.itemsData) {
          const quantity = Number(item.quantity) || 1;
          const sales = parseFloat(item.sales) || 0;

          grandTotal.quantity += quantity;
          grandTotal.sales += sales;
        }
      }

      grandTotal.sales = parseFloat(grandTotal.sales.toFixed(2));

      modifiedInvoiceCustomer = modifiedInvoiceCustomer.filter((item) => {
        return item?.itemsData.length > 0;
      });
    }

    let conversionSummaryData = [];
    if (showConversionSummary) {
      const groupedInvoices = invoiceOfCustomer.reduce((acc, invoice) => {
        const customerId = invoice?.customer?.id;
        if (!acc[customerId]) {
          acc[customerId] = {
            customerId: customerId,
            customerName:
              invoice.customer.firstName + ' ' + invoice.customer.lastName,
            total: 0,
            status: 'New Customer',
          };
        }
        acc[customerId].total += invoice.paid;
        return acc;
      }, {});
      conversionSummaryData = Object.values(groupedInvoices);
    }

    if (isExport) {
      modifiedInvoiceCustomer.push({
        itemsData: [grandTotal],
        sum: 'grand_total',
      });
      type ExportDataType = {
        order: number | string;
        date?: string;
        customerFullName?: string;
        code?: string;
        employeeName?: any;
        itemsData?: any;
      };
      let firstExportData: ExportDataType[] = modifiedInvoiceCustomer.map(
        (item, index) => {
          return {
            order: item.sum === 'grand_total' ? '' : index + 1,
            date:
              item.sum === 'grand_total'
                ? ''
                : moment
                    .tz(item.date, clientZoneName)
                    .format('DD/MM/YYYY h:mm A'),
            customerFullName:
              item.sum === 'grand_total'
                ? ''
                : item?.customer?.code +
                  ' ' +
                  item?.customer?.firstName +
                  ' ' +
                  item?.customer?.lastName,
            code: item.sum === 'grand_total' ? '' : `${item.code}`,
            employeeName:
              item.sum === 'grand_total'
                ? ''
                : item.referral?.username || item.referral?.fullname || '',
            itemsData: item.itemsData,
            note: item.note,
          };
        },
      );

      if (showConversionSummary) {
        firstExportData.push(
          { order: 'Conversion Summary' },
          {
            order: '#',
            date: 'CUSTOMER',
            code: 'TOTAL (SGD)',
            customerFullName: 'STATUS',
          },
        );

        // merge data conversionSummaryData vào firstExportData
        firstExportData = firstExportData.concat(
          conversionSummaryData.map((item, index) => {
            return {
              order: index + 1,
              date: item.customerName,
              code: item.total.toFixed(2),
              customerFullName: 'New Customer',
            };
          }),
        );
      }

      return firstExportData;
    }
    return {
      data: modifiedInvoiceCustomer,
      conversionSummaryData,
      grandTotal,
    };
  }

  async getReportPrepaidServiceSale(
    req: CrudRequest,
    {
      startDate,
      endDate,
      type,
      saleEmployee,
      clientZoneName,
    }: ReportPrepaidServiceSaleQueryDTO,
    isExport = false,
  ) {
    let categoryArr = [];
    if (type) {
      categoryArr = type.split(',');
    } else {
      // Default to include membership (package), service, and coupon (combo coupon) types
      categoryArr = [
        ProductType.MEMBERSHIP,
        ProductType.SERVICE,
        ProductType.COUPON,
      ];
    }

    // const data: any = {
    //   startTime: moment().startOf('month').format('YYYY-MM-DDTHH:mm:ss.SSS'),
    //   endTime: moment().endOf('month').format('YYYY-MM-DDTHH:mm:ss.SSS'),
    // };
    // if (startDate) {
    //   data.startTime = moment(startDate)
    //     .startOf('day')
    //     .format('YYYY-MM-DDTHH:mm:ss.SSS');
    // }
    // if (endDate) {
    //   data.endTime = moment(endDate)
    //     .endOf('day')
    //     .format('YYYY-MM-DDTHH:mm:ss.SSS');
    // }

    const utcStartTime = startDate;
    const utcEndTime = endDate;

    const whereClause: Record<string, any> = {
      status: In([InvoiceStatus.PAID]),
      created: Between(utcStartTime, utcEndTime),
    };

    if (categoryArr.length > 0) {
      whereClause.orders = {
        items: {
          product: {
            type: In(categoryArr),
          },
        },
      };
    }

    if (saleEmployee) {
      const saleEmployeeArr = saleEmployee.split(',');
      whereClause.referral = {
        id: In(saleEmployeeArr),
      };
    }
    const invoiceOfCustomer = await this.invoiceRepo.find({
      where: whereClause,
      order: {
        created: 'ASC',
      },
      relations: ['orders', 'referral', 'customer'],
    });

    let modifiedInvoiceCustomer = [];
    if (invoiceOfCustomer.length > 0) {
      const CustomerListPromises = invoiceOfCustomer.map(async (invoice) => {
        const itemsByCategory = [];
        let countByCategory = 0;
        if (invoice?.orders?.length > 0) {
          for (const order of invoice?.orders) {
            const orderPayload: any = order.payload;
            if (orderPayload && orderPayload.items) {
              const items: any[] = orderPayload.items;
              for (const item of items) {
                // const money = (item?.quantity ?? 1) * (item?.product?.price ?? 0);
                if (
                  categoryArr.length > 0 &&
                  categoryArr.includes(item?.product?.type)
                ) {
                  const objectService = {
                    item: item?.product?.name ?? '',
                    quantity: item?.quantity ?? 1,
                    gross: 0,
                    net: 0,
                    charge: 0,
                    paid: 0,
                    mins: 0,
                  };
                  if (
                    item?.product?.type === ProductType.SERVICE ||
                    item?.product?.type === ProductType.COUPON
                  ) {
                    // Calculate minutes duration for both service and coupon (combo) items
                    objectService.mins = await this.minutesBetweenDates(
                      item?.startTime,
                      item?.endTime,
                    );
                  }
                  itemsByCategory[countByCategory] = objectService;
                  countByCategory++;
                }
              }
            }
          }
        }
        delete invoice.orders;

        return {
          date: invoice?.created,
          referenceNo: invoice?.code,
          customer: invoice?.customer,
          saleEmployee: invoice?.referral,
          itemsData: itemsByCategory,
        };
      });

      modifiedInvoiceCustomer = await Promise.all(CustomerListPromises);
      modifiedInvoiceCustomer = modifiedInvoiceCustomer.filter((item) => {
        return item?.itemsData.length > 0;
      });
    }
    const grandTotal = {
      quantity: 0,
      gross: 0,
      net: 0,
      charge: 0,
      paid: 0,
    };
    for (const invoice of modifiedInvoiceCustomer) {
      for (const item of invoice.itemsData) {
        grandTotal.quantity += item.quantity || 1;
        grandTotal.gross += item.gross || 0;
        grandTotal.net += item.net || 0;
        grandTotal.charge += item.charge || 0;
        grandTotal.paid += item.paid || 0;
      }
    }
    modifiedInvoiceCustomer.push({
      itemsData: [grandTotal],
      sum: 'grand_total',
    });

    if (isExport) {
      return modifiedInvoiceCustomer.map((item, index) => {
        return {
          order: item.sum === 'grand_total' ? null : index + 1,
          date:
            item.sum === 'grand_total'
              ? null
              : moment
                  .tz(item.date, clientZoneName)
                  .format('DD/MM/YYYY h:mm A'),
          customerFullName:
            item.sum === 'grand_total'
              ? null
              : item.customer?.code +
                ' ' +
                item.customer?.firstName +
                ' ' +
                item.customer?.lastName,
          code: item.sum === 'grand_total' ? null : `IN${item.referenceNo}`,
          employeeName:
            item.saleEmployee?.fullname || item.saleEmployee?.username,
          remark: item.note,
          itemsData: item.itemsData?.map((product) => {
            return {
              ...product,
              quantity: product?.quantity,
              gross: (product?.gross || 0).toFixed(2),
              net: (product?.net || 0).toFixed(2),
              charge: (product?.charge || 0).toFixed(2),
              paid: (product?.paid || 0).toFixed(2),
              mins: item.sum === 'grand_total' ? 'GRAND TOTAL:' : product.mins,
            };
          }),
        };
      });
    }

    return {
      data: modifiedInvoiceCustomer,
    };
  }

  async getReportEmployeePerformance(
    req: CrudRequest,
    { startDate, endDate, branchIds, showInactiveTherapists }: ReportQueryDTO,
    isExport = false,
  ) {
    const checkShowInactive = showInactiveTherapists == 'true';
    console.log('startDate: ', startDate);
    console.log('endDate: ', endDate);

    const whereClause: Record<string, any> = {};
    if (branchIds && branchIds.length > 0) {
      whereClause.branch = [
        {
          id: In(branchIds),
        },
        {
          id: IsNull(),
        },
      ];
    }

    if (!checkShowInactive) {
      whereClause.status = [
        {
          name: 'Active',
        },
        {
          name: IsNull(),
        },
      ];
    }

    const userList = await this.userRepo.find({
      where: whereClause,
    });
    // Sort userList by username ASC
    userList.sort((a, b) => (a.username || '').localeCompare(b.username || ''));
    const queryOrders = this.orderRepo
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .where(
        'branch.id ' +
          (branchIds && branchIds.length
            ? 'IN (:...branchIds)'
            : 'IS NOT NULL'),
        { branchIds },
      )
      .andWhere('invoice.id IS NOT NULL')
      .andWhere('invoice.status IN  (:...statusArr)', {
        statusArr: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      });
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryOrders.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    const orders = await queryOrders.getMany();
    const dataReferral = [];
    for (let i = 0; i < userList.length; i++) {
      dataReferral[i] = {
        productSummary: 0,
        serviceSummary: 0,
        membershipSummary: 0,
        foodBeverageSummary: 0,
        couponSummary: 0,
        saleTotal: 0,
        employee: userList[i],
      };
    }
    for (const order of orders) {
      const orderPayload: any = order.payload;
      const referralId = order?.invoice?.referral?.id;
      const index = userList.findIndex((obj) => obj.id === referralId);

      if (index !== -1 && orderPayload && orderPayload.items) {
        const invoice = order.invoice;

        // Detect if invoice is paid by credit
        const isPaidByCredit = Array.isArray(invoice.invoicePayments)
          ? invoice.invoicePayments.some(
              (payment) =>
                payment?.paymentMethod?.code?.toLowerCase() ===
                  PaymentMethod.CREDIT ||
                payment?.paymentMethod?.code === CreditType.NEW ||
                payment?.paymentMethod?.code === CreditType.OLD,
            )
          : false;

        // Calculate discount percentage
        const diff = invoice.paid - invoice.subTotal;
        const percent =
          invoice.subTotal > 0 ? Math.abs(diff / invoice.subTotal) * 100 : 0;

        const items: any[] = orderPayload.items;
        for (const item of items) {
          const originalMoney =
            (item?.quantity ?? 1) * (item?.product?.price ?? 0);

          // Apply discount calculation like in getReportNewCustomerSale
          let money: number;
          if (isPaidByCredit) {
            money = 0; // If paid by credit, sales = 0.00
          } else if (diff >= 0) {
            // Markup case (paid more than subtotal)
            money = originalMoney + (originalMoney * percent) / 100;
          } else {
            // Discount case (paid less than subtotal)
            money = originalMoney - (originalMoney * percent) / 100;
          }

          switch (item?.product?.type) {
            case ProductType.PRODUCT:
              dataReferral[index]['productSummary'] += money;
              break;
            case ProductType.SERVICE:
              dataReferral[index]['serviceSummary'] += money;
              break;
            case ProductType.MEMBERSHIP:
              dataReferral[index]['membershipSummary'] += money;
              break;
            case ProductType.COUPON:
              dataReferral[index]['couponSummary'] += money;
              break;
            case ProductType.FOOD:
            case ProductType.BEVERAGE:
              dataReferral[index]['foodBeverageSummary'] += money;
              break;
            default:
              break;
          }
        }
      }
    }
    const dataRes = dataReferral.map((referral) => {
      return {
        ...referral,
        saleTotal:
          referral.foodBeverageSummary +
          referral.membershipSummary +
          referral.serviceSummary +
          referral.couponSummary +
          referral.productSummary,
      };
    });

    const grandTotal = {
      productSummary: 0,
      serviceSummary: 0,
      membershipSummary: 0,
      foodBeverageSummary: 0,
      couponSummary: 0,
      saleTotal: 0,
      sum: 'grand_total',
    };
    for (const user of dataRes) {
      grandTotal.productSummary += user.productSummary || 0;
      grandTotal.serviceSummary += user.serviceSummary || 0;
      grandTotal.membershipSummary += user.membershipSummary || 0;
      grandTotal.foodBeverageSummary += user.foodBeverageSummary || 0;
      grandTotal.couponSummary += user.couponSummary || 0;
      grandTotal.saleTotal += user.saleTotal || 0;
    }
    dataRes.push(grandTotal);
    if (isExport) {
      return dataRes.map((item, index) => {
        return {
          order: item.sum !== 'grand_total' ? index + 1 : null,
          // code: item.employee?.code ?? '',
          name:
            item.sum === 'grand_total'
              ? 'GRAND TOTAL:'
              : item.employee?.username ?? '',
          package: (item.membershipSummary || 0).toFixed(2),
          product: (item.productSummary || 0).toFixed(2),
          service: (item.serviceSummary || 0).toFixed(2),
          fAndB: (item.foodBeverageSummary || 0).toFixed(2),
          saleTotal: (item.saleTotal || 0).toFixed(2),
          coupon: (item.couponSummary || 0).toFixed(2),
        };
      });
    }

    return {
      data: dataRes,
    };
  }

  async getReportEmployeeServiceDetail(
    req: CrudRequest,
    {
      startDate,
      endDate,
      branchIds,
      keySearch,
      saleEmployee,
      clientZoneName,
    }: ReportEmployeeSaleServiceQueryDTO,
    isExport = false,
  ) {
    const whereClause: Record<string, any> = {};
    if (branchIds && branchIds.length > 0) {
      whereClause.branch = { id: In(branchIds) };
    }
    const queryOrders = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('orderDetail.employees', 'employees')
      .where(
        'branch.id ' +
          (branchIds && branchIds.length
            ? 'IN (:...branchIds)'
            : 'IS NOT NULL'),
        { branchIds },
      )
      .andWhere(`product.type = :type`, { type: ProductType.SERVICE })
      .andWhere('invoice.id IS NOT NULL')
      .andWhere('invoice.status IN  (:...statusArr)', {
        statusArr: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .orderBy('orderDetail.created', 'ASC');

    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryOrders.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }
    if (keySearch) {
      queryOrders.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', { code: keySearch }),
        ),
      );
    }
    if (saleEmployee) {
      const saleEmployeeArr = saleEmployee.split(',');
      queryOrders.andWhere('employees.id IN (:...saleEmployeeArr)', {
        saleEmployeeArr,
      });
    }
    queryOrders.select([
      'customer.firstName',
      'customer.lastName',
      'customer.code',
      'invoice.code',
      'invoice.date',
      'invoice.paid',
      'invoice.subTotal',
      'product.name',
      'category.name',
      'product.price',
      'orderDetail.quantity',
      'order.tax',
      'orderDetail.startTime',
      'orderDetail.endTime',
      'referral.displayName',
      'employees.id',
      'employees.displayName',
    ]);
    const orders = await queryOrders.getRawMany();

    const result = [];
    if (orders.length > 0) {
      const groups = {};
      for (const item of orders) {
        const diff = item.invoice_paid - item.invoice_subTotal;
        const percentDiff = Math.abs(diff / item.invoice_subTotal);

        // Determine employee name - use "Unassigned" if no employee is assigned
        const employeeName = item.employees_displayName || 'Unassigned';

        if (!groups[employeeName]) {
          groups[employeeName] = {
            employee: employeeName,
            detail: [],
            quantity: 0,
            duration: 0,
            value: 0,
            actualValue: 0,
          };
          result.push(groups[employeeName]);
        }
        const duration = await this.minutesBetweenDates(
          item.orderDetail_startTime,
          item.orderDetail_endTime,
        );

        item.actualValue =
          diff >= 0
            ? item.product_price + item.product_price * percentDiff
            : item.product_price - item.product_price * percentDiff;

        item.value = item.product_price;
        groups[employeeName].detail.push({
          ...item,
          duration,
        });
        groups[employeeName].quantity += item.orderDetail_quantity;
        groups[employeeName].duration += duration;
        groups[employeeName].value += item.value;
        groups[employeeName].actualValue += item.actualValue;
        groups[employeeName].value = parseFloat(
          groups[employeeName].value.toFixed(2),
        );
      }
    }

    if (isExport) {
      const exportData = [];
      for (const item of result) {
        exportData.push({ service: item.employee });
        let order = 1;
        for (const detail of item.detail) {
          exportData.push({
            order: order++,
            date: moment
              .tz(detail.invoice_date, clientZoneName)
              .format('DD/MM/YYYY h:mm A'),
            customerFullName:
              detail.customer_code +
              ' ' +
              detail.customer_firstName +
              ' ' +
              detail.customer_lastName,
            customerCode: detail.customer_code,
            invoiceCode: `IN${detail.invoice_code}`,
            service: detail.product_name,
            category: detail.category_name,
            price: (detail.product_price || 0).toFixed(2),
            quantity: detail.quantity,
            duration: detail.duration,
            value: (detail.value || 0).toFixed(2),
            actualValue: (detail.actualValue || 0).toFixed(2),
          });
        }
        exportData.push({
          sum: 'grand_total',
          quantity: item.quantity,
          duration: item.duration,
          value: item.value.toFixed(2),
          actualValue: item.actualValue.toFixed(2),
          foc: 'GRAND TOTAL:',
        });
      }

      return exportData;
    }

    return {
      data: result,
    };
  }

  async getReportNewCustomerByEmployee(
    req: CrudRequest,
    { startDate, endDate, branchIds }: ReportEmployeeSaleServiceQueryDTO,
  ) {
    const whereClause: Record<string, any> = {};
    if (branchIds && branchIds.length > 0) {
      whereClause.branch = { id: In(branchIds) };
    }
    const userList = await this.userRepo.find({
      where: whereClause,
    });
    const queryOrders = this.orderRepo
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .where(
        'branch.id ' +
          (branchIds && branchIds.length
            ? 'IN (:...branchIds)'
            : 'IS NOT NULL'),
        { branchIds },
      )
      .andWhere('invoice.id IS NOT NULL')
      .andWhere('invoice.status IN  (:...statusArr)', {
        statusArr: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      });
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryOrders.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
      queryOrders.andWhere('customer.created BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    const orders = await queryOrders.getMany();
    const dataReferral = [];
    for (let i = 0; i < userList.length; i++) {
      dataReferral[i] = {
        productSummary: 0,
        serviceSummary: 0,
        packageSummary: 0,
        foodBeverageSummary: 0,
        totalCustomer: 0,
        employee: userList[i],
      };
    }

    for (const order of orders) {
      const orderPayload: any = order.payload;
      const referralId = order?.invoice?.referral?.id;
      const customerId = order?.invoice?.customer?.id;
      const index = userList.findIndex((obj) => obj.id === referralId);
      if (!dataReferral[index]['totalCustomer']) {
        dataReferral[index]['totalCustomer'] = [];
      }
      if (!dataReferral[index]['totalCustomer'].includes(customerId)) {
        dataReferral[index]['totalCustomer'].push(customerId);
      }
      if (orderPayload && orderPayload.items) {
        const items: any[] = orderPayload.items;
        for (const item of items) {
          const money = (item?.quantity ?? 1) * (item?.product?.price ?? 0);
          switch (item?.product?.type) {
            case ProductType.PRODUCT:
              dataReferral[index]['productSummary'] += money;
              if (!dataReferral[index]['productCustomer']) {
                dataReferral[index]['productCustomer'] = [];
              }
              if (
                !dataReferral[index]['productCustomer'].includes(customerId)
              ) {
                dataReferral[index]['productCustomer'].push(customerId);
              }
            case ProductType.SERVICE:
              dataReferral[index]['serviceSummary'] += money;
              if (!dataReferral[index]['serviceCustomer']) {
                dataReferral[index]['serviceCustomer'] = [];
              }
              if (
                !dataReferral[index]['serviceCustomer'].includes(customerId)
              ) {
                dataReferral[index]['serviceCustomer'].push(customerId);
              }
              break;
            case ProductType.MEMBERSHIP:
              dataReferral[index]['packageSummary'] += money;
              if (!dataReferral[index]['packageCustomer']) {
                dataReferral[index]['packageCustomer'] = [];
              }
              if (
                !dataReferral[index]['packageCustomer'].includes(customerId)
              ) {
                dataReferral[index]['packageCustomer'].push(customerId);
              }
              break;
            case ProductType.FOOD:
            case ProductType.BEVERAGE:
              dataReferral[index]['foodBeverageSummary'] += money;
              if (!dataReferral[index]['foodBeverageCustomer']) {
                dataReferral[index]['foodBeverageCustomer'] = [];
              }
              if (
                !dataReferral[index]['foodBeverageCustomer'].includes(
                  customerId,
                )
              ) {
                dataReferral[index]['foodBeverageCustomer'].push(customerId);
              }
              break;
            default:
              break;
          }
        }
      }
    }

    const dataRes = dataReferral.map((referral) => {
      const foodBeverageCustomerCount = referral.foodBeverageCustomer
        ? referral.foodBeverageCustomer.length
        : 0;
      const packageCustomerCount = referral.packageCustomer
        ? referral.packageCustomer.length
        : 0;
      const serviceCustomerCount = referral.serviceCustomer
        ? referral.serviceCustomer.length
        : 0;
      const productCustomerCount = referral.productCustomer
        ? referral.productCustomer.length
        : 0;
      const totalCustomerCount = referral.totalCustomer
        ? referral.totalCustomer.length
        : 0;
      [
        'packageCustomer',
        'foodBeverageCustomer',
        'serviceCustomer',
        'productCustomer',
        'totalCustomer',
      ].forEach((property) => {
        delete referral[property];
      });
      return {
        ...referral,
        foodBeverageCustomerCount,
        packageCustomerCount,
        serviceCustomerCount,
        productCustomerCount,
        totalSummary:
          referral.foodBeverageSummary +
          referral.packageSummary +
          referral.serviceSummary +
          referral.productSummary,
        totalCustomerCount,
      };
    });

    const grandTotal = {
      productSummary: 0,
      serviceSummary: 0,
      packageSummary: 0,
      foodBeverageSummary: 0,
      totalSummary: 0,
      foodBeverageCustomerCount: 0,
      packageCustomerCount: 0,
      serviceCustomerCount: 0,
      productCustomerCount: 0,
      totalCustomerCount: 0,
      sum: 'grand_total',
    };
    for (const user of dataRes) {
      grandTotal.productSummary += user.productSummary || 0;
      grandTotal.serviceSummary += user.serviceSummary || 0;
      grandTotal.packageSummary += user.packageSummary || 0;
      grandTotal.foodBeverageSummary += user.foodBeverageSummary || 0;
      grandTotal.totalSummary += user.totalSummary || 0;
      grandTotal.productCustomerCount += user.productCustomerCount || 0;
      grandTotal.serviceCustomerCount += user.serviceCustomerCount || 0;
      grandTotal.packageCustomerCount += user.packageCustomerCount || 0;
      grandTotal.foodBeverageCustomerCount +=
        user.foodBeverageCustomerCount || 0;
      grandTotal.totalCustomerCount += user.totalCustomerCount || 0;
    }
    dataRes.push(grandTotal);
    return {
      data: dataRes,
    };
  }

  async minutesBetweenDates(startTimeStr, endTimeStr) {
    const startTime = new Date(startTimeStr);
    const endTime = new Date(endTimeStr);
    const timeDifference = endTime.getTime() - startTime.getTime();
    const minutesDifference = Math.round(timeDifference / (1000 * 60));
    return minutesDifference;
  }
}
